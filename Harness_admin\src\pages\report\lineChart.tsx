import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, Toolt<PERSON> } from 'recharts';

interface ChartData {
  month: string;
  developers?: number;
  investors?: number;
  [key: string]: string | number | undefined;
}

const CustomLineTooltip = ({ active, payload, label }: {
  active?: boolean;
  payload?: Array<{
    color: string;
    dataKey: string;
    value: number;
  }>;
  label?: string;
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-900 mb-1">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.dataKey === 'developers' ? 'Developers' : 'Investors'}: {entry.value.toLocaleString()}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const LineChartComponent = ({ data, data<PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, chartType }: {
  data: ChartData[];
  dataKey: string;
  selectedMonth: string;
  chartType: string;
}) => (
  <div className="h-64">
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data} key={`${chartType}-${selectedMonth}`}>
        <XAxis 
          dataKey="month" 
          axisLine={true}
          tickLine={true}
          tick={{ fontSize: 12, fill: '#6b7280' }}
          stroke="#e5e7eb"
        />
        <YAxis 
          axisLine={true}
          tickLine={true}
          tick={{ fontSize: 12, fill: '#6b7280' }}
          stroke="#e5e7eb"
          domain={[0, 1500]}
          ticks={[0, 50, 100, 500, 1000, 1500]}
        />
        <Tooltip content={<CustomLineTooltip />} />
        <defs>
          <pattern id={`${chartType}Grid`} width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill={`url(#${chartType}Grid)`} />
        <Line 
          type="monotone" 
          dataKey={dataKey} 
          stroke="#f59e0b" 
          strokeWidth={2}
          dot={false}
        />
        <defs>
          <linearGradient id={`${chartType}Gradient`} x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.1}/>
            <stop offset="95%" stopColor="#f59e0b" stopOpacity={0}/>
          </linearGradient>
        </defs>
      </LineChart>
    </ResponsiveContainer>
  </div>
);

export default LineChartComponent;